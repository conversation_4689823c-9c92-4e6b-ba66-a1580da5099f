import os
import re
import sys

def rename_files(directory):
    # 检查目录是否存在
    if not os.path.isdir(directory):
        print(f"错误：目录 '{directory}' 不存在")
        return
    
    # 获取指定目录下的所有文件
    files = [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
    
    # 匹配类似 "pancreas_290.nii.gz" 的文件名模式
    pattern = r'^(pancreas_\d+)(\.nii\.gz)$'
    
    for filename in files:
        # 尝试匹配文件名模式
        match = re.match(pattern, filename)
        if match:
            # 提取文件名的前缀和后缀
            prefix = match.group(1)
            suffix = match.group(2)
            
            # 构建新的文件名
            new_filename = f"{prefix}_0000{suffix}"
            
            # 构建完整的文件路径
            old_path = os.path.join(directory, filename)
            new_path = os.path.join(directory, new_filename)
            
            # 重命名文件
            os.rename(old_path, new_path)
            print(f"已重命名: {filename} -> {new_filename}")

if __name__ == "__main__":
    # 确定要处理的目录，优先使用命令行参数，否则使用当前目录
    if len(sys.argv) > 1:
        target_directory = sys.argv[1]
    else:
        target_directory = os.getcwd()
    
    print(f"正在处理目录: {target_directory}")
    rename_files(target_directory)
    print("文件名修改完成！")
    